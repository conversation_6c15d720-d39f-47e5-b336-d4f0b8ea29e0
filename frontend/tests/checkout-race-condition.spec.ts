import { test, expect } from '@playwright/test';

test.describe('Checkout Race Condition Bug Reproduction', () => {
  let sessionId: string;
  let cartData: any;
  let checkoutRequestData: any;

  test.beforeEach(async ({ page }) => {
    // Clear localStorage to ensure clean state
    await page.goto('/');
    await page.evaluate(() => {
      localStorage.clear();
    });

    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Wait for initial session creation
    await page.waitForTimeout(3000);
  });

  test('should reproduce race condition with network interruption during checkout', async ({ page }) => {
    console.log('=== Starting Checkout Race Condition Test ===');

    // Step 1: Add items to cart first (since we already have a guest session)
    console.log('Step 1: Adding items to cart...');

    // Add the first available product to cart
    const firstAddButton = page.locator('button').filter({ hasText: 'ADD' }).first();
    await expect(firstAddButton).toBeVisible({ timeout: 10000 });
    await firstAddButton.click();
    await page.waitForTimeout(1000);

    // Add another item to have multiple items in cart
    const secondAddButton = page.locator('button').filter({ hasText: 'ADD' }).nth(1);
    if (await secondAddButton.isVisible()) {
      await secondAddButton.click();
      await page.waitForTimeout(1000);
    }

    // Step 2: Open cart drawer
    console.log('Step 2: Opening cart drawer...');
    const cartButton = page.locator('button').filter({ hasText: /My Cart/i }).first();
    await expect(cartButton).toBeVisible();
    await cartButton.click();
    await page.waitForTimeout(2000);

    // Step 3: Capture session data before checkout
    console.log('Step 3: Capturing session data...');
    const sessionData = await page.evaluate(() => {
      return {
        sessions: localStorage.getItem('infinity_sessions'),
        activeSessionId: localStorage.getItem('infinity_active_session_id')
      };
    });

    sessionId = JSON.parse(sessionData.activeSessionId || '""');
    cartData = JSON.parse(sessionData.sessions || '[]');

    console.log('Session ID:', sessionId);
    console.log('Cart Data:', cartData);

    // Verify we have session data
    expect(sessionId).toBeTruthy();
    expect(cartData).toBeTruthy();
    expect(Array.isArray(cartData)).toBe(true);
    expect(cartData.length).toBeGreaterThan(0);

    // Find the active session and verify it has cart items
    const activeSession = cartData.find((s: any) => s.id === sessionId);
    expect(activeSession).toBeTruthy();
    expect(activeSession.cartItems).toBeTruthy();
    expect(activeSession.cartItems.length).toBeGreaterThan(0);

    console.log('Active session cart items:', activeSession.cartItems.length);

    // Step 4: Set up network interruption for checkout
    console.log('Step 4: Setting up network interruption...');

    let checkoutRequestCaptured = false;

    // Intercept checkout API calls
    await page.route('**/userApp-infinity-upsertCartByBhumi', async (route, request) => {
      console.log('Intercepted checkout request:', request.url());
      checkoutRequestData = await request.postDataJSON();
      checkoutRequestCaptured = true;

      // Simulate network interruption - abort the request after backend would have processed
      // This simulates the scenario where backend processes successfully but frontend doesn't receive response
      setTimeout(() => {
        route.abort('failed');
      }, 100);
    });

    // Also intercept the admin endpoint in case it's used
    await page.route('**/userApp-infinity-upsertCart', async (route, request) => {
      console.log('Intercepted admin checkout request:', request.url());
      checkoutRequestData = await request.postDataJSON();
      checkoutRequestCaptured = true;

      setTimeout(() => {
        route.abort('failed');
      }, 100);
    });

    // Step 5: Initiate checkout
    console.log('Step 5: Initiating checkout...');

    // Look for checkout button in the cart drawer
    const checkoutButton = page.locator('button').filter({ hasText: /checkout|place order|proceed/i }).first();
    await expect(checkoutButton).toBeVisible({ timeout: 10000 });
    await checkoutButton.click();
    await page.waitForTimeout(2000);

    // Fill checkout form if it appears
    console.log('Step 6: Filling checkout form...');
    const customerNameInput = page.locator('input[placeholder*="name"], input[name*="name"]').first();
    if (await customerNameInput.isVisible()) {
      await customerNameInput.fill('Test Customer');
      await page.waitForTimeout(500);
    }

    const customerPhoneInput = page.locator('input[placeholder*="phone"], input[name*="phone"]').first();
    if (await customerPhoneInput.isVisible()) {
      await customerPhoneInput.fill('9894715170');
      await page.waitForTimeout(500);
    }

    // Look for place order button
    const placeOrderButton = page.locator('button').filter({ hasText: /place order|confirm|submit/i }).first();
    if (await placeOrderButton.isVisible()) {
      console.log('Step 7: Submitting order (network will be interrupted)...');
      await placeOrderButton.click();
    }

    // Wait for network interruption to occur
    await page.waitForTimeout(5000);

    // Step 8: Verify session persistence after network interruption
    console.log('Step 8: Verifying session persistence...');

    const sessionDataAfterInterruption = await page.evaluate(() => {
      return {
        sessions: localStorage.getItem('infinity_sessions'),
        activeSessionId: localStorage.getItem('infinity_active_session_id')
      };
    });

    // Verify that session data persists (this is the bug - it should be cleared on successful checkout)
    expect(sessionDataAfterInterruption.activeSessionId).toBeTruthy();
    expect(sessionDataAfterInterruption.sessions).toBeTruthy();

    const persistedSessions = JSON.parse(sessionDataAfterInterruption.sessions || '[]');
    const persistedActiveSessionId = JSON.parse(sessionDataAfterInterruption.activeSessionId || '""');

    console.log('Persisted Session ID:', persistedActiveSessionId);
    console.log('Persisted Sessions:', persistedSessions);

    // Verify the session ID matches what we captured before
    expect(persistedActiveSessionId).toBe(sessionId);

    // Verify cart data is still present
    const activeSessionAfterInterruption = persistedSessions.find((s: any) => s.id === persistedActiveSessionId);
    expect(activeSessionAfterInterruption).toBeTruthy();
    expect(activeSessionAfterInterruption.cartItems).toBeTruthy();
    expect(activeSessionAfterInterruption.cartItems.length).toBeGreaterThan(0);

    console.log('✅ Race condition reproduced: Session and cart data persisted after network interruption');

    // Verify that checkout request was captured
    expect(checkoutRequestCaptured).toBe(true);
    expect(checkoutRequestData).toBeTruthy();

    console.log('Captured checkout request data:', checkoutRequestData);

    // Document the exact checkout request format for analysis
    console.log('Checkout request structure:');
    console.log('- cartId:', checkoutRequestData.cartId);
    console.log('- userType:', checkoutRequestData.userType);
    console.log('- status:', checkoutRequestData.status);
    console.log('- cartJson.skuItems count:', checkoutRequestData.cartJson?.skuItems?.length);
    console.log('- cartJson.customer:', checkoutRequestData.cartJson?.customer);
  });

  test('should handle duplicate submission after network interruption', async ({ page }) => {
    console.log('=== Testing Duplicate Submission ===');

    // Set up the same session state as after network interruption
    await page.evaluate(() => {
      // Simulate the state after network interruption where session persists
      const mockSession = {
        id: 'test-session-id',
        customerName: 'Test Customer',
        cartItems: [
          { skuId: 50710, quantity: 1 },
          { skuId: 50662, quantity: 1 }
        ],
        lastActive: new Date().toISOString()
      };

      localStorage.setItem('infinity_sessions', JSON.stringify([mockSession]));
      localStorage.setItem('infinity_active_session_id', JSON.stringify('test-session-id'));
    });

    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Capture backend error response for duplicate submission
    let duplicateSubmissionError: any = null;

    await page.route('**/userApp-infinity-upsertCartByBhumi', async (route, request) => {
      console.log('Intercepted duplicate submission request');

      // Simulate backend error response for already processed cart
      const errorResponse = {
        status: false,
        error: {
          message: 'Cart has already been processed',
          code: 'CART_ALREADY_PROCESSED',
          cartId: 'test-session-id'
        }
      };

      duplicateSubmissionError = errorResponse.error;

      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify(errorResponse)
      });
    });

    // Also intercept admin endpoint
    await page.route('**/userApp-infinity-upsertCart', async (route, request) => {
      console.log('Intercepted duplicate admin submission request');

      const errorResponse = {
        status: false,
        error: {
          message: 'Cart has already been processed',
          code: 'CART_ALREADY_PROCESSED',
          cartId: 'test-session-id'
        }
      };

      duplicateSubmissionError = errorResponse.error;

      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify(errorResponse)
      });
    });

    // Attempt to submit checkout again
    console.log('Attempting duplicate submission...');

    // Open cart and try checkout again
    const cartButton = page.locator('button').filter({ hasText: /My Cart/i }).first();
    await expect(cartButton).toBeVisible({ timeout: 10000 });
    await cartButton.click();
    await page.waitForTimeout(2000);

    const checkoutButton = page.locator('button').filter({ hasText: /checkout|place order|proceed/i }).first();
    await expect(checkoutButton).toBeVisible({ timeout: 10000 });
    await checkoutButton.click();
    await page.waitForTimeout(2000);

    // Fill form and submit
    const customerNameInput = page.locator('input[placeholder*="name"], input[name*="name"]').first();
    if (await customerNameInput.isVisible()) {
      await customerNameInput.fill('Test Customer');
    }

    const customerPhoneInput = page.locator('input[placeholder*="phone"], input[name*="phone"]').first();
    if (await customerPhoneInput.isVisible()) {
      await customerPhoneInput.fill('9894715170');
    }

    const placeOrderButton = page.locator('button').filter({ hasText: /place order|confirm|submit/i }).first();
    if (await placeOrderButton.isVisible()) {
      await placeOrderButton.click();
    }

    await page.waitForTimeout(3000);

    // Verify error response was captured
    expect(duplicateSubmissionError).toBeTruthy();
    expect(duplicateSubmissionError.code).toBe('CART_ALREADY_PROCESSED');
    expect(duplicateSubmissionError.message).toContain('already been processed');

    console.log('✅ Duplicate submission error captured:', duplicateSubmissionError);
    console.log('Backend error format documented for fix implementation');

    // Document the exact error format for the fix implementation
    console.log('Error response structure:');
    console.log('- error.code:', duplicateSubmissionError.code);
    console.log('- error.message:', duplicateSubmissionError.message);
    console.log('- error.cartId:', duplicateSubmissionError.cartId);
  });
});
