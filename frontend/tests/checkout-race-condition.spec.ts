import { test, expect } from '@playwright/test';

test.describe('Checkout Race Condition Bug Reproduction', () => {
  let sessionId: string;
  let cartData: any;
  let checkoutRequestData: any;

  // Helper function to handle modal overlays and ensure UI is ready
  async function ensureUIReady(page: any) {
    // Wait for any loading states to complete
    await page.waitForLoadState('networkidle');

    // Check for and handle any modal overlays that might be blocking interactions
    const modalOverlay = page.locator('.ReactModal__Overlay--after-open');

    if (await modalOverlay.isVisible()) {
      console.log('Modal overlay detected, attempting to handle...');

      // Try clicking directly on the overlay to close it (some modals close on overlay click)
      try {
        await modalOverlay.click({ timeout: 1000 });
        await page.waitForTimeout(500);
      } catch (error) {
        console.log('Overlay click failed, trying other methods...');
      }

      // Try to find and click close button (X, Close, etc.)
      const closeSelectors = [
        'button[aria-label*="close"]',
        'button:has-text("×")',
        'button:has-text("Close")',
        '[data-testid="close-button"]',
        '.close-button',
        '.ReactModal__Content button:last-child', // Often the last button is close/cancel
        '.ReactModal__Content [role="button"]:has-text("Cancel")',
        '.ReactModal__Content [role="button"]:has-text("Skip")'
      ];

      let modalClosed = false;
      for (const selector of closeSelectors) {
        const closeButton = page.locator(selector).first();
        if (await closeButton.isVisible()) {
          try {
            await closeButton.click({ timeout: 1000 });
            await page.waitForTimeout(500);
            modalClosed = true;
            console.log(`Modal closed using selector: ${selector}`);
            break;
          } catch (error) {
            console.log(`Failed to click close button with selector: ${selector}`);
          }
        }
      }

      // If no close button found, try pressing Escape multiple times
      if (!modalClosed) {
        console.log('Trying Escape key to close modal...');
        await page.keyboard.press('Escape');
        await page.waitForTimeout(500);
        await page.keyboard.press('Escape');
        await page.waitForTimeout(500);
      }

      // Wait for modal to close
      await page.waitForSelector('.ReactModal__Overlay--after-open', {
        state: 'hidden',
        timeout: 3000
      }).catch(() => {
        console.log('Modal overlay still present, but continuing...');
      });
    }

    // Additional wait to ensure DOM is stable
    await page.waitForTimeout(1000);
  }

  // Helper function to safely click elements, handling potential overlays
  async function safeClick(page: any, locator: any, description: string) {
    console.log(`Attempting to click: ${description}`);

    // Ensure element is visible and ready
    await expect(locator).toBeVisible({ timeout: 10000 });

    // First attempt: normal click
    try {
      await locator.click({ timeout: 3000 });
      console.log(`✅ Successfully clicked: ${description}`);
      await page.waitForTimeout(500);
      return;
    } catch (error) {
      console.log(`❌ Normal click failed for ${description}, trying alternative approaches...`);
    }

    // Second attempt: handle overlays and retry
    try {
      await ensureUIReady(page);
      await locator.click({ timeout: 3000 });
      console.log(`✅ Successfully clicked after overlay handling: ${description}`);
      await page.waitForTimeout(500);
      return;
    } catch (error) {
      console.log(`❌ Click after overlay handling failed for ${description}, trying force click...`);
    }

    // Third attempt: force click
    try {
      await locator.click({ force: true, timeout: 3000 });
      console.log(`✅ Force clicked: ${description}`);
      await page.waitForTimeout(500);
      return;
    } catch (error) {
      console.log(`❌ Force click failed for ${description}, trying JavaScript click...`);
    }

    // Fourth attempt: JavaScript click
    try {
      await locator.evaluate((element: any) => element.click());
      console.log(`✅ JavaScript clicked: ${description}`);
      await page.waitForTimeout(500);
      return;
    } catch (error) {
      console.log(`❌ All click attempts failed for ${description}`);
      throw new Error(`Failed to click element: ${description}`);
    }
  }

  test.beforeEach(async ({ page }) => {
    console.log('Setting up test environment...');

    // Clear localStorage to ensure clean state
    await page.goto('/');
    await page.evaluate(() => {
      localStorage.clear();
    });

    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Wait for initial session creation
    await page.waitForTimeout(3000);

    // Handle the login modal that appears
    console.log('Handling login modal...');

    // Wait for the login modal to appear
    const mobileInput = page.locator('input[placeholder*="mobile number"], input[placeholder*="Mobile Number"]');
    await mobileInput.waitFor({ timeout: 10000 });

    // Fill in the mobile number (using the test number from memory)
    console.log('Filling mobile number...');
    await mobileInput.fill('9894715170');

    // Click Send OTP button and wait for the request to complete
    console.log('Clicking Send OTP and waiting for request...');
    const sendOtpButton = page.locator('button:has-text("Send OTP")');

    // Wait for the OTP request to be sent and completed
    const otpRequestPromise = page.waitForResponse(response =>
      response.url().includes('otp') || response.url().includes('send') || response.url().includes('login')
    );

    await sendOtpButton.click();

    try {
      await otpRequestPromise;
      console.log('OTP request completed');
    } catch (error) {
      console.log('OTP request timeout, continuing...');
    }

    // Wait a bit more for UI to update
    await page.waitForTimeout(2000);

    // Take a screenshot to see what's on screen after OTP request
    await page.screenshot({ path: 'debug-after-otp-request.png', fullPage: true });
    console.log('Screenshot taken after OTP request');

    // Wait for OTP input to appear and fill it
    console.log('Waiting for OTP input field...');

    // Try multiple possible selectors for OTP input
    const otpSelectors = [
      'input[placeholder*="OTP"]',
      'input[placeholder*="otp"]',
      'input[placeholder*="Enter OTP"]',
      'input[placeholder*="verification"]',
      'input[type="text"]:not([placeholder*="mobile"]):not([placeholder*="Mobile"])',
      'input[type="number"]',
      'input[maxlength="4"]',
      'input[maxlength="6"]'
    ];

    let otpInput = null;
    for (const selector of otpSelectors) {
      const input = page.locator(selector);
      if (await input.isVisible()) {
        otpInput = input;
        console.log(`Found OTP input with selector: ${selector}`);
        break;
      }
    }

    if (otpInput) {
      console.log('OTP input found, filling OTP...');
      await otpInput.fill('0000');

      // Submit OTP
      const submitButton = page.locator('button:has-text("Verify"), button:has-text("Submit"), button:has-text("Login")');
      if (await submitButton.isVisible()) {
        console.log('Submitting OTP...');
        await submitButton.click();

        // Wait for login completion
        await page.waitForTimeout(3000);
      }
    } else {
      console.log('OTP input not found with any selector, continuing...');
      // Take another screenshot to debug
      await page.screenshot({ path: 'debug-no-otp-input.png', fullPage: true });
    }

    console.log('Test environment ready');
  });

  test('should reproduce race condition with network interruption during checkout', async ({ page }) => {
    console.log('=== Starting Checkout Race Condition Test ===');

    // Step 1: Add items to cart (login should be completed now)
    console.log('Step 1: Adding items to cart...');

    // Find and click the first ADD button
    const firstAddButton = page.locator('button').filter({ hasText: 'ADD' }).first();
    await safeClick(page, firstAddButton, 'First ADD button');

    // Add a second item if available
    const secondAddButton = page.locator('button').filter({ hasText: 'ADD' }).nth(1);
    if (await secondAddButton.isVisible()) {
      await safeClick(page, secondAddButton, 'Second ADD button');
    }

    // Step 2: Open cart drawer
    console.log('Step 2: Opening cart drawer...');
    const cartButton = page.locator('button').filter({ hasText: /My Cart/i }).first();
    console.log('Force clicking My Cart button...');
    await cartButton.click({ force: true });

    // Wait for cart drawer to open
    await page.waitForTimeout(2000);

    // Step 3: Capture session data before checkout
    console.log('Step 3: Capturing session data...');
    const sessionData = await page.evaluate(() => {
      return {
        sessions: localStorage.getItem('infinity_sessions'),
        activeSessionId: localStorage.getItem('infinity_active_session_id')
      };
    });

    sessionId = JSON.parse(sessionData.activeSessionId || '""');
    cartData = JSON.parse(sessionData.sessions || '[]');

    console.log('Session ID:', sessionId);
    console.log('Cart Data length:', cartData?.length);

    // Verify we have session data
    expect(sessionId).toBeTruthy();
    expect(cartData).toBeTruthy();
    expect(Array.isArray(cartData)).toBe(true);
    expect(cartData.length).toBeGreaterThan(0);

    // Find the active session and verify it has cart items
    const activeSession = cartData.find((s: any) => s.id === sessionId);
    expect(activeSession).toBeTruthy();
    expect(activeSession.cartItems).toBeTruthy();
    expect(activeSession.cartItems.length).toBeGreaterThan(0);

    console.log('Active session cart items:', activeSession.cartItems.length);

    // Step 4: Set up network interruption for checkout
    console.log('Step 4: Setting up network interruption...');

    let checkoutRequestCaptured = false;

    // Intercept checkout API calls
    await page.route('**/userApp-infinity-upsertCartByBhumi', async (route, request) => {
      console.log('🔥 Intercepted checkout request:', request.url());
      checkoutRequestData = await request.postDataJSON();
      checkoutRequestCaptured = true;

      // Simulate network interruption - abort the request after backend would have processed
      setTimeout(() => {
        console.log('🔥 Simulating network interruption...');
        route.abort('failed');
      }, 100);
    });

    // Also intercept the admin endpoint
    await page.route('**/userApp-infinity-upsertCart', async (route, request) => {
      console.log('🔥 Intercepted admin checkout request:', request.url());
      checkoutRequestData = await request.postDataJSON();
      checkoutRequestCaptured = true;

      setTimeout(() => {
        console.log('🔥 Simulating network interruption...');
        route.abort('failed');
      }, 100);
    });

    // Step 5: Initiate checkout
    console.log('Step 5: Initiating checkout...');

    // Look for checkout button in the cart drawer
    const checkoutButton = page.locator('button').filter({ hasText: /checkout|place order|proceed/i }).first();
    await safeClick(page, checkoutButton, 'Checkout button');

    // Step 6: Fill checkout form
    console.log('Step 6: Filling checkout form...');

    // Wait for checkout modal to appear
    await page.waitForTimeout(2000);

    // Fill customer name if field is present
    const customerNameInput = page.locator('input[placeholder*="name"], input[name*="name"]').first();
    if (await customerNameInput.isVisible()) {
      await customerNameInput.fill('Test Customer');
      await page.waitForTimeout(500);
    }

    // Fill customer phone if field is present
    const customerPhoneInput = page.locator('input[placeholder*="phone"], input[name*="phone"]').first();
    if (await customerPhoneInput.isVisible()) {
      await customerPhoneInput.fill('9894715170');
      await page.waitForTimeout(500);
    }

    // Step 7: Submit order (this will trigger network interruption)
    console.log('Step 7: Submitting order (network will be interrupted)...');

    const placeOrderButton = page.locator('button').filter({ hasText: /place order|confirm|submit/i }).first();
    if (await placeOrderButton.isVisible()) {
      await safeClick(page, placeOrderButton, 'Place Order button');
    }

    // Wait for network interruption to occur and any error handling
    await page.waitForTimeout(5000);

    // Step 8: Verify session persistence after network interruption
    console.log('Step 8: Verifying session persistence...');

    const sessionDataAfterInterruption = await page.evaluate(() => {
      return {
        sessions: localStorage.getItem('infinity_sessions'),
        activeSessionId: localStorage.getItem('infinity_active_session_id')
      };
    });

    // Verify that session data persists (this is the bug)
    expect(sessionDataAfterInterruption.activeSessionId).toBeTruthy();
    expect(sessionDataAfterInterruption.sessions).toBeTruthy();

    const persistedSessions = JSON.parse(sessionDataAfterInterruption.sessions || '[]');
    const persistedActiveSessionId = JSON.parse(sessionDataAfterInterruption.activeSessionId || '""');

    console.log('Persisted Session ID:', persistedActiveSessionId);
    console.log('Persisted Sessions count:', persistedSessions.length);

    // Verify the session ID matches what we captured before
    expect(persistedActiveSessionId).toBe(sessionId);

    // Verify cart data is still present
    const activeSessionAfterInterruption = persistedSessions.find((s: any) => s.id === persistedActiveSessionId);
    expect(activeSessionAfterInterruption).toBeTruthy();
    expect(activeSessionAfterInterruption.cartItems).toBeTruthy();
    expect(activeSessionAfterInterruption.cartItems.length).toBeGreaterThan(0);

    console.log('🐛 BUG REPRODUCED: Session and cart data persisted after network interruption');

    // Verify that checkout request was captured
    expect(checkoutRequestCaptured).toBe(true);
    expect(checkoutRequestData).toBeTruthy();

    console.log('✅ Checkout request captured during network interruption');
    console.log('Checkout request structure:');
    console.log('- cartId:', checkoutRequestData.cartId);
    console.log('- userType:', checkoutRequestData.userType);
    console.log('- status:', checkoutRequestData.status);
    console.log('- cartJson.skuItems count:', checkoutRequestData.cartJson?.skuItems?.length);
  });
});